import argparse
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import pandas as pd

# ------------------------------------------------------------------------------------
# Helpers
# ------------------------------------------------------------------------------------

def infer_route_from_bag(bag_dir: Path) -> str:
    name = bag_dir.name.lower()
    route = "unknown"
    if "r1" in name:
        route = "R1"
    elif "r2" in name:
        route = "R2"
    elif "r3" in name:
        route = "R3"
    if "left" in name:
        route += "-left"
    elif "right" in name:
        route += "-right"
    return route


def normalize_bool_label(val) -> Optional[bool]:
    if val is None:
        return None
    if isinstance(val, (int, float)):
        # Treat 1/true as True, 0/false as False
        if val == 1:
            return True
        if val == 0:
            return False
    s = str(val).strip().lower()
    if s in {"1", "true", "yes", "y", "positive", "pos", "moving", "moved"}:
        return True
    if s in {"0", "false", "no", "n", "negative", "neg", "stationary", "none"}:
        return False
    return None


def parse_env_pred(text: str) -> Optional[bool]:
    if not isinstance(text, str) or not text.strip():
        return None
    s = text.strip().lower()
    # Expecting strictly 'yes' or 'no' per prompts, but be flexible
    if "yes" in s and "no" not in s:
        return True
    if "no" in s and "yes" not in s:
        return False
    # Fallback heuristics
    if any(k in s for k in ["moving", "independently moving", "self-moving"]):
        return True
    if any(k in s for k in ["no movement", "stationary only", "no moving object"]):
        return False
    return None


def parse_vehicle_reasoning_pred(text: str) -> Optional[str]:
    """
    Parse vehicle reasoning outputs into one of:
    - 'lidar_malfunction'
    - 'visual_misaligned'
    - 'normal'
    Returns None if unknown.
    """
    if not isinstance(text, str) or not text.strip():
        return None
    s = text.strip().lower()
    # Normal first
    if any(k in s for k in ["normal", "no abnormal", "operating correctly", "aligned properly"]):
        # But exclude negations like 'not normal'
        if "not normal" not in s and "abnormal" not in s and "misalign" not in s and "mis-orient" not in s:
            return "normal"
    # LiDAR malfunction cues
    if any(k in s for k in ["lidar", "li-dar", "li dar"]):
        if any(k in s for k in ["malfunction", "abnormal", "fault", "broken", "failure", "misaligned", "mis-orient", "mis oriented"]):
            return "lidar_malfunction"
    # Visual/camera misalignment cues
    if any(k in s for k in ["visual", "camera", "cam"]):
        if any(k in s for k in ["misaligned", "mis-orient", "misoriented", "not facing forward", "wrong direction", "turned"]):
            return "visual_misaligned"
    # Generic misalignment mention without explicit sensor
    if any(k in s for k in ["misaligned", "misalignment", "mis-orient", "misoriented"]):
        # Assume camera misalignment by default
        return "visual_misaligned"
    return None


# ------------------------------------------------------------------------------------
# Evaluation routines
# ------------------------------------------------------------------------------------

def _parse_speed_value(token: str) -> Optional[float]:
    # token example: "<2489>19.9km/h"
    token = token.strip()
    if not token:
        return None
    # find number before km/h
    try:
        kmh_part = token.split(">", 1)[-1].lower()
        if "km/h" in kmh_part:
            val = kmh_part.split("km/h", 1)[0]
        else:
            # fallback: last numeric in token
            val = ''.join(ch if (ch.isdigit() or ch=='.') else ' ' for ch in token).split()
            val = val[-1] if val else ''
        return float(val) if val else None
    except Exception:
        return None


def _parse_filename_ts_s(name: str) -> Optional[float]:
    # Extract numeric stem, infer units by magnitude
    if not isinstance(name, str):
        return None
    base = name.rsplit("/", 1)[-1].rsplit("\\", 1)[-1]
    stem = base.split(".")[0]
    digits = ''.join(ch for ch in stem if ch.isdigit())
    if not digits:
        return None
    try:
        n = int(digits)
    except Exception:
        return None
    # infer
    if n >= 10**15:
        return n / 1e9   # ns -> s
    if n >= 10**12:
        return n / 1e6   # µs -> s
    if n >= 10**9:
        return n / 1e3   # ms -> s
    return float(n)      # s


def derive_env_gt_from_speed(bag: Path, df: pd.DataFrame, fname_cols: List[str], tol_s: float = 0.6,
                              min_speed_kmh: float = 0.5) -> Optional[pd.Series]:
    """Return a boolean Series aligned to df.index indicating if any object is moving per frame.
    Uses nearest timestamp match between image filenames and objectinformation/object_speed.txt.
    """
    speed_file = bag / "objectinformation" / "object_speed.txt"
    if not speed_file.exists():
        return None
    # Load speed entries
    ts_to_any = {}
    try:
        with speed_file.open("r", encoding="utf-8") as f:
            for line in f:
                parts = line.strip().split(",")
                if len(parts) < 3:
                    continue
                try:
                    ts = float(parts[0])
                except Exception:
                    continue
                spd = _parse_speed_value(parts[2])
                if spd is None:
                    continue
                if ts not in ts_to_any:
                    ts_to_any[ts] = (spd >= min_speed_kmh)
                else:
                    ts_to_any[ts] = ts_to_any[ts] or (spd >= min_speed_kmh)
    except Exception:
        return None

    if not ts_to_any:
        return None

    # Prepare dataframes for nearest merge
    import pandas as pd  # local alias
    speed_df = pd.DataFrame({"ts": sorted(ts_to_any.keys())})
    speed_df["moving"] = speed_df["ts"].map(ts_to_any)

    # Build image timestamps
    ts_img = []
    idx_map = []
    fname_col = None
    for c in fname_cols:
        if c in df.columns:
            fname_col = c
            break
    if not fname_col:
        return None
    for i, val in df[fname_col].items():
        t = _parse_filename_ts_s(str(val))
        ts_img.append(t)
        idx_map.append(i)
    img_df = pd.DataFrame({"idx": idx_map, "ts_img": ts_img}).dropna()
    if img_df.empty:
        return None

    img_df = img_df.sort_values("ts_img")
    speed_df = speed_df.sort_values("ts")

    try:
        merged = pd.merge_asof(img_df, speed_df, left_on="ts_img", right_on="ts", direction="nearest",
                               tolerance=tol_s)
    except Exception:
        return None
    # Map back to original index
    env_gt = pd.Series(False, index=df.index)
    for _, row in merged.dropna(subset=["moving"]).iterrows():
        env_gt.at[row["idx"]] = bool(row["moving"])
    return env_gt

def eval_environment(df: pd.DataFrame, pred_col: str, gt_col: Optional[str]) -> Tuple[int, int]:
    ok = 0
    tot = 0
    for _, row in df.iterrows():
        pred = parse_env_pred(row.get(pred_col, ""))
        gt = normalize_bool_label(row.get(gt_col)) if gt_col else None
        if pred is None or gt is None:
            continue
        tot += 1
        ok += int(pred == gt)
    return ok, tot


def eval_vehicle_lidar(df: pd.DataFrame, pred_col: str, gt_col: Optional[str]) -> Tuple[int, int]:
    ok = 0
    tot = 0
    for _, row in df.iterrows():
        label = parse_vehicle_reasoning_pred(row.get(pred_col, ""))
        if label is None:
            continue
        pred = (label == "lidar_malfunction")
        gt = normalize_bool_label(row.get(gt_col)) if gt_col else None
        if gt is None:
            continue
        tot += 1
        ok += int(pred == gt)
    return ok, tot


def eval_vehicle_vision(df: pd.DataFrame, pred_col: str, gt_col: Optional[str]) -> Tuple[int, int]:
    ok = 0
    tot = 0
    for _, row in df.iterrows():
        label = parse_vehicle_reasoning_pred(row.get(pred_col, ""))
        if label is None:
            continue
        pred = (label == "visual_misaligned")
        gt = normalize_bool_label(row.get(gt_col)) if gt_col else None
        if gt is None:
            continue
        tot += 1
        ok += int(pred == gt)
    return ok, tot


# ------------------------------------------------------------------------------------
# CLI
# ------------------------------------------------------------------------------------

def inspect_bags(bags: List[Path]) -> None:
    """Print union of CSV columns and sample headers per route/bag to ease GT mapping."""
    if not bags:
        print("No bag folders to inspect.")
        return
    union_cols = set()
    per_route_samples: Dict[str, List[Tuple[Path, List[str]]]] = {}
    for bag in bags:
        csv_path = bag / "bag_information" / "object_descriptions.csv"
        if not csv_path.exists():
            alt_csv = bag / "visual_descriptions.csv"
            if alt_csv.exists():
                csv_path = alt_csv
            else:
                continue
        try:
            df = pd.read_csv(csv_path, nrows=2)
        except Exception as e:
            print(f"Failed reading {csv_path}: {e}")
            continue
        union_cols.update(df.columns.tolist())
        route = infer_route_from_bag(bag)
        per_route_samples.setdefault(route, [])
        if len(per_route_samples[route]) < 2:
            per_route_samples[route].append((csv_path, df.columns.tolist()))

    print("\nUnion of CSV columns across discovered bags:")
    for c in sorted(union_cols):
        print(f"  - {c}")

    print("\nSample headers per route (up to 2 each):")
    for route in sorted(per_route_samples.keys()):
        print(f"\nRoute: {route}")
        for path, cols in per_route_samples[route]:
            print(f"  {path}")
            print(f"    columns: {', '.join(cols)}")

def main():
    ap = argparse.ArgumentParser(description="Evaluate DriveAgent outputs against ground truth.")
    ap.add_argument("--data-root", type=Path, default=Path("."), help="Root directory containing bag folders (hou-*)")
    ap.add_argument("--routes", nargs="*", default=None, help="Optional route filter, e.g. R2 R2-left R2-right R3")
    ap.add_argument("--task", choices=["all", "env", "vehicle-lidar", "vehicle-vision"], default="all")
    ap.add_argument("--inspect", action="store_true", help="List CSV headers and union of columns, then exit.")
    # Prediction columns (defaults based on our pipelines)
    ap.add_argument("--pred-col-env", default="environment_aggregation")
    ap.add_argument("--pred-col-lidar", default="vehicle_abnormal_analysis_output")
    ap.add_argument("--pred-col-vision", default="vehicle_abnormal_analysis_output")
    # Ground-truth columns
    ap.add_argument("--gt-col-env", default=None, help="Ground truth column for environment moving object presence")
    ap.add_argument("--gt-col-lidar", default=None, help="Ground truth column for LiDAR malfunction (bool)")
    ap.add_argument("--gt-col-vision", default=None, help="Ground truth column for camera misalignment (bool)")
    args = ap.parse_args()

    data_root: Path = args.data_root.resolve()

    # Gather bag folders
    bags: List[Path] = [
        d for d in data_root.iterdir()
        if d.is_dir() and (d.name.startswith("hou-") or d.name in ("R1", "R2", "R3"))
    ]
    if not bags:
        print("No bag folders found under", data_root)
        return

    # Inspect mode: help discover available columns
    if args.inspect:
        if args.routes:
            bags = [b for b in bags if infer_route_from_bag(b) in args.routes]
        inspect_bags(bags)
        return

    # Aggregate per-route
    per_route: Dict[str, Dict[str, Tuple[int, int]]] = {}

    for bag in bags:
        route = infer_route_from_bag(bag)
        if args.routes and route not in args.routes:
            continue
        csv_path = bag / "bag_information" / "object_descriptions.csv"
        if not csv_path.exists():
            alt_csv = bag / "visual_descriptions.csv"
            if alt_csv.exists():
                csv_path = alt_csv
            else:
                continue
        df = pd.read_csv(csv_path)

        if route not in per_route:
            per_route[route] = {"env": (0, 0), "vehicle-lidar": (0, 0), "vehicle-vision": (0, 0)}

        if args.task in ("all", "env"):
            # Try automatic GT derivation if no GT column provided
            env_gt_col = args.gt_col_env
            if env_gt_col is None:
                derived = derive_env_gt_from_speed(
                    bag,
                    df,
                    fname_cols=["env_picture_title", "Filename"],
                )
                if derived is not None:
                    df["__derived_env_gt__"] = derived
                    env_gt_col = "__derived_env_gt__"
            ok, tot = eval_environment(df, args.pred_col_env, env_gt_col)
            a_ok, a_tot = per_route[route]["env"]
            per_route[route]["env"] = (a_ok + ok, a_tot + tot)

        if args.task in ("all", "vehicle-lidar"):
            ok, tot = eval_vehicle_lidar(df, args.pred_col_lidar, args.gt_col_lidar)
            a_ok, a_tot = per_route[route]["vehicle-lidar"]
            per_route[route]["vehicle-lidar"] = (a_ok + ok, a_tot + tot)

        if args.task in ("all", "vehicle-vision"):
            ok, tot = eval_vehicle_vision(df, args.pred_col_vision, args.gt_col_vision)
            a_ok, a_tot = per_route[route]["vehicle-vision"]
            per_route[route]["vehicle-vision"] = (a_ok + ok, a_tot + tot)

    # Print summary
    any_data = False
    print("\nEvaluation summary:")
    for route in sorted(per_route.keys()):
        print(f"\nRoute: {route}")
        for key in ["env", "vehicle-lidar", "vehicle-vision"]:
            ok, tot = per_route[route][key]
            if tot > 0:
                any_data = True
                acc = 100.0 * ok / tot
                print(f"  {key:15s}  acc={acc:6.2f}%  (n={tot})  ok={ok}")
            else:
                print(f"  {key:15s}  acc=   n/a   (n=0)")

    if not any_data:
        print("No evaluable rows found. Make sure your ground-truth columns are provided via --gt-col-* and that predictions exist.")
        print("Tip: try --task env --pred-col-env environment_aggregation --gt-col-env <your_gt_col>.")


if __name__ == "__main__":
    main()
