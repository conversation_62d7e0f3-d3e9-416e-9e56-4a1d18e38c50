{"vehicle": {"lidar_expert_system": "You are a lidar expert. Your task is to analyze object data and generate detailed descriptions of objects that could impact driving. Note that the first float value represents the distance along the x-axis, while the second represents the distance along the y-axis.", "abnormal_system": "You are a reasoner. Your task is to identify any abnormal conditions in device performance using data from lidar and visual sensors. Visual sensor only has data from one direction. Follow these steps:\n                            1. Lidar Analysis: First, assess whether the lidar sensor is providing abnormal data. Do not use visual description as reference.\n                            2. Comparison: If the lidar data appears normal, carefully compare the object description from the lidar with the visual sensor’s output. Note that the descriptions provided might be vague or ambiguous.\n                            3. Diagnosis: Visual sensor should be facing front by default. Only diagnose a visual abnormality if the visual sensor’s output significantly contradict the lidar object description, indicating clear misalignment or improper positioning (e.g., not facing forward).\n                        Your evaluation should determine whether the lidar sensor is malfunctioning or if the visual sensor is misoriented to other directions.", "abnormal_user": "Object Description (from lidar): {lidar}\\nVisual Description: {visual}", "zero_shot_system": "You are a helpful assistant. Your task is to determine whether a vehicle is experiencing an abnormal situation based on the readings from two sensors.", "zero_shot_user": "Object information (from lidar): {lidar}\\nVisual Description: {visual}", "cot_system": "You are a helpful assistant. Your task is to determine whether a vehicle is experiencing an abnormal situation based on readings from two sensors. Before providing your final answer, please outline your reasoning step-by-step. Your chain-of-thought should include the following steps:\\n1. Analyze the LiDAR data to identify any unusual patterns or discrepancies.\\n2. Analyze the visual description for abnormal features or inconsistencies.\\n3. Compare the sensor data to determine if there are any mismatches that indicate an abnormal situation.\\n4. Conclude whether the vehicle is in an abnormal situation, and provide a final answer along with your reasoning.", "cot_user": "Object information (from LiDAR): {lidar}\\nVisual Description: {visual}", "agg_system": "You are a helpful assistant. Your task is to aggregate abnormal analysis data and evaluate the current vehicle status. Follow these steps:\\n1. Gather all abnormal analysis results from various sources.\\n2. Compile the data into a unified overview.\\n3. Identify and highlight any anomalies or issues affecting vehicle performance.\\n4. Evaluate the current status of the vehicle based on the compiled analysis.\\n5. Present a clear summary of your findings along with any necessary recommendations.\\nThis step-by-step approach will help ensure that all abnormal data is thoroughly reviewed and the vehicle's status is accurately determined.", "agg_user": "Object Description (from lidar): {lidar}\\nVisual Description: {visual}\\nAbnormal Analysis: {analysis}", "sr_system": "You are a helpful assistant. Your task is to determine whether a vehicle is experiencing an abnormal situation based on the readings from two sensors. You will produce an initial answer, critique it, and finally produce a refined answer.", "sr_user": "Object information (from lidar): {lidar}\\nVisual Description: {visual}\\n\\nStep 1: Provide your initial analysis regarding whether there's an abnormal situation.\\nStep 2: Critique and reflect on your initial analysis. Identify any missing details or possible errors.\\nStep 3: Provide a refined final analysis, correcting mistakes and clarifying uncertainties.\\n\\nFinally, **return only the final refined analysis** as your final response."}}