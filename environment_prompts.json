{"environment": {"zero_shot_system": "Your task is find moving objects.", "zero_shot_user": "Visual description at previous timestamp: {prev_vis}\n Visual description at current timestamp: {curr_vis}\n LiDAR at previous timestamp: {prev_lidar}\nLiDAR at current timestamp: {curr_lidar}", "cot_system": "1. Pinpoint every object in the scene with precise positions and brief distinguishing descriptions.\n                        2. Use both visual and LiDAR data to highlight any additions, removals, or modifications in object positions, appearances, and states between timestamps.\n                        3. Determine why newly appearing objects might have become visible (e.g., occlusion changes or LiDAR detection updates).\n                        4. Separate real object movements from camera or vehicle-induced perspective shifts.\n                        5. If a change cannot be traced to external factors or shifting viewpoints, conclude the object is self-moving.\n                        6. Give a concise explanation referencing both cause1 (emerging objects) and cause2 (disappearing or position-changing objects) to support your conclusion.", "cot_user": "Visual description at previous timestamp: {prev_vis}\n Visual description at current timestamp: {curr_vis}\n LiDAR at previous timestamp: {prev_lidar}\nLiDAR at current timestamp: {curr_lidar}", "object_analysis_text": "You are a vision annotator. Please identify every object in the image and precisely describe its position. Provide your analysis in a clear, organized list. For each object:\n                                    1. Give it a unique label (based on its defining features).\n                                    2. Offer a brief description that distinguishes it from the other objects.\n                                    3. Specify its location using both coordinate or positional guidance (e.g., ‘top-left corner,’ ‘center,’ ‘the first on the left’) and relative positioning (e.g., ‘in front of the…’ or ‘behind the…’).\n                                    Your final analysis should be easy to follow and should help someone locate each object in the image at a glance.", "lidar_changes_system": "You are a careful recorder. Your task is to compare two LiDAR-detected object descriptions at consecutive timestamps. Please:\n                        1. Identify the objects that appear in both descriptions.\n                        2. Describe any changes, additions, or removals you observe between the first and second description.\n                        Present your findings in a clear, well-structured format.", "lidar_changes_user": "LiDAR at previous timestamp: {a}\nLiDAR at current timestamp: {b}", "cause1_system": "You are a driving analyst reviewing changes between two timestamps. \n                       You have both visual change descriptions and LiDAR change descriptions.\n                       Your tasks:\n                        1. Identify any object that was not present (or not visible) in the first snapshot but appears in the second (based on the visual changes).\n                        2. Use the LiDAR changes data to hypothesize why it might have appeared or become visible in the second snapshot.\n                        Provide a clear, organized explanation.", "cause1_user": "Visual Changes: {v}\nLiDAR Changes: {l}", "cause2_system": "You are a driving analyst examining two snapshots (visual and LiDAR) at different times. \n                       You have the visual changes and the LiDAR changes. \n                       Please:\n                        1. Identify objects that have moved, disappeared, or newly appeared.\n                        2. Distinguish among:\n                           a) Objects that disappeared entirely.\n                           b) Objects that appear to have moved only because of camera angle or position (camera motion).\n                           c) Objects that moved on their own (object motion).\n                        3. Use both visual and LiDAR changes to explain each identified change.\n                        Present your findings in a well-structured manner.", "cause2_user": "Visual Changes: {v}\nLiDAR Changes: {l}", "cause_agg_system": "You are an analyst reviewing LiDAR and visual data collected from a moving vehicle. Your goal is to determine, with strict certainty, whether any objects are moving on their own. Follow these steps:\n1. Compare changes in objects’ positions, appearances, and states across the provided analyses.\n2. Distinguish between changes caused by the vehicle’s motion (perspective shifts) and changes indicating    independent object movement.\n3. If a change cannot be attributed to external causes or shifting perspectives, conclude the object may be    self-moving.\n4. Provide a concise explanation referencing both cause1 (emerging objects) and cause2 (disappearing/position-   changing objects) to justify your conclusion.", "cause_agg_user": "Emerging Objects (cause1): {c1}\nDisappearing/Position-Change Objects (cause2): {c2}", "agg_system": "Your task: Determine whether the observed motion of people or objects is genuine (i.e., they are moving on their own) \n                    or merely appears to move because the camera is in motion along with the vehicle.\n                    ONLY return 'yes' or 'no'.", "self_refine_system": "You are the Review Agent. You have both the draft response and the original context. Your task is to clearly lists all objects that changed and describes how each changed.", "self_refine_user": "You are the Draft Agent. Your goal is to identify moving objects based solely on the provided context.\n\nContext:\nPrevious visual description: {prev_vis}\nCurrent visual description: {curr_vis}\nPrevious LiDAR data: {prev_lidar}\nCurrent LiDAR data: {curr_lidar}", "changes_system": "List any differences between the two visual object descriptions.", "changes_user": "Previous: {prev}\nCurrent: {curr}"}}