import argparse
import base64
import json
import os
from pathlib import Path
from typing import Any, Dict, List

import pandas as pd
from openai import OpenAI, OpenAIError

# --------------------------------------------------------------------------------------------------------------------------------------
# -----------------------------------------------------------CLI ARGUMENTS --------------------------------------------------------------
# --------------------------------------------------------------------------------------------------------------------------------------

def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(description="LiDAR/vision change‑analysis pipeline")
    p.add_argument("--prompts", type=Path, default=Path("environment_prompts.json"),
                   help="Path to prompts.json file")
    p.add_argument("--api-key", type=str, default=os.getenv("OPENAI_API_KEY"),
                   help="OpenAI API key (overrides environment variable)")
    p.add_argument("--data-root", type=Path, default=Path("."),
                   help="Root directory that holds bag folders (hou-*)")
    p.add_argument("--bags", nargs="*", default=None,
                   help="Specific bag folders to process (default: all hou-*)")
    p.add_argument("--framework", choices=["zero-shot", "cot", "self-refine", "full"],
                   default="full", help="Prompting framework to use")
    p.add_argument("--max-rows", type=int, default=None,
                   help="Optional cap on number of rows to process per CSV (applies to PASS 2).")
    return p.parse_args()

ARGS = parse_args()

# --------------------------------------------------------------------------------------------------------------------------------------
# -----------------------------------------------------------GLOBALS & INITIALISATION ---------------------------------------------------
# --------------------------------------------------------------------------------------------------------------------------------------
ROOT_DIR: Path = ARGS.data_root.resolve()
PROMPTS_FILE: Path = ARGS.prompts.resolve()
FRAMEWORK: str = ARGS.framework  # single source of truth downstream
MAX_ROWS: int | None = ARGS.max_rows

with open(PROMPTS_FILE, "r", encoding="utf-8") as fp:
    raw_prompts: Dict[str, Any] = json.load(fp)
if "environment" in raw_prompts and isinstance(raw_prompts["environment"], dict):
    PROMPTS: Dict[str, str] = raw_prompts["environment"]
else:
    PROMPTS = raw_prompts  # already flat

# Initialize OpenAI client with API key (CLI arg overrides env)
os.environ["OPENAI_API_KEY"] = ARGS.api_key or os.getenv("OPENAI_API_KEY", "")
if not os.environ["OPENAI_API_KEY"]:
    raise RuntimeError("OpenAI API key not provided via --api-key or env var")
client = OpenAI(api_key=os.environ["OPENAI_API_KEY"])

# --------------------------------------------------------------------------------------------------------------------------------------
# ----------------------------------------------------------- HELPERS --------------------------------------------------------------------
# --------------------------------------------------------------------------------------------------------------------------------------

def get_prompt(key: str) -> str:
    try:
        return PROMPTS[key]
    except KeyError as exc:
        raise KeyError(f"Prompt '{key}' missing in {PROMPTS_FILE}") from exc


def chat_completion(model: str, messages: List[Dict[str, Any]]) -> str:
    try:
        resp = client.chat.completions.create(model=model, messages=messages, temperature=0)
        return resp.choices[0].message.content
    except OpenAIError as exc:
        raise RuntimeError(f"OpenAI call failed: {exc}") from exc


def encode_image(img_path: Path) -> str:
    with img_path.open("rb") as img:
        return base64.b64encode(img.read()).decode()


# DEMO visual change helper ------------------------------

def visual_changes(prev_vis: str, curr_vis: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("changes_system")},
            {"role": "user", "content": get_prompt("changes_user").format(prev=prev_vis, curr=curr_vis)}
        ],
    )


# --------------------------------------------------------------------------------------------------------------------------------------
# ----------------------------------------------------------- ADDITIONAL MODEL WRAPPERS --------------------------------------------------
# --------------------------------------------------------------------------------------------------------------------------------------

def lidar_changes_analysis(prev_lidar: str, curr_lidar: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("lidar_changes_system")},
            {"role": "user", "content": get_prompt("lidar_changes_user").format(a=prev_lidar, b=curr_lidar)},
        ],
    )


def cause1_analysis(v_changes: str, l_changes: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("cause1_system")},
            {"role": "user", "content": get_prompt("cause1_user").format(v=v_changes, l=l_changes)},
        ],
    )


def cause2_analysis(v_changes: str, l_changes: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("cause2_system")},
            {"role": "user", "content": get_prompt("cause2_user").format(v=v_changes, l=l_changes)},
        ],
    )


def cause_aggregation(c1: str, c2: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("cause_agg_system")},
            {"role": "user", "content": get_prompt("cause_agg_user").format(c1=c1, c2=c2)},
        ],
    )


def aggregation(context: str) -> str:
    # Minimal yes/no aggregator using the provided context string
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("agg_system")},
            {"role": "user", "content": context},
        ],
    ).strip()


def self_refine_objects(prev_vis: str, curr_vis: str, prev_lidar: str, curr_lidar: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("self_refine_system")},
            {"role": "user", "content": get_prompt("self_refine_user").format(
                prev_vis=prev_vis, curr_vis=curr_vis, prev_lidar=prev_lidar, curr_lidar=curr_lidar
            )},
        ],
    )


# --------------------------------------------------------------------------------------------------------------------------------------
# ----------------------------------------------------------- MODEL WRAPPERS (UNCHANGED LOGIC) -------------------------------------------
# --------------------------------------------------------------------------------------------------------------------------------------

def zero_shot(prev_vis: str, curr_vis: str, prev_lidar: str, curr_lidar: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("zero_shot_system")},
            {"role": "user", "content": get_prompt("zero_shot_user").format(
                prev_vis=prev_vis, curr_vis=curr_vis, prev_lidar=prev_lidar, curr_lidar=curr_lidar)}
        ],
    )


def cot(prev_vis: str, curr_vis: str, prev_lidar: str, curr_lidar: str) -> str:
    return chat_completion(
        "gpt-4o",
        [
            {"role": "system", "content": get_prompt("cot_system")},
            {"role": "user", "content": get_prompt("cot_user").format(
                prev_vis=prev_vis, curr_vis=curr_vis, prev_lidar=prev_lidar, curr_lidar=curr_lidar)}
        ],
    )


def object_analysis(b64_img: str) -> str:
    return chat_completion(
        "gpt-4o-mini",
        [{
            "role": "user",
            "content": [
                {"type": "text", "text": get_prompt("object_analysis_text")},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{b64_img}"}},
            ],
        }],
    )


# ----------------------------------------------------------------------------------------------------------------------------------------
# ----------------------------------------------------------- PROCESSING LOGIC -----------------------------------------------------------
# ----------------------------------------------------------------------------------------------------------------------------------------

def ensure_cols(df: pd.DataFrame, cols: List[str]):
    for col in cols:
        if col not in df.columns:
            df[col] = ""


def process_bag(bag_path: Path):
    # Prefer standard bag layout
    csv_path = bag_path / "bag_information" / "object_descriptions.csv"
    if not csv_path.exists():
        # Fallback to route layout (R1/R2/R3): visual_descriptions.csv at route root
        alt_csv = bag_path / "visual_descriptions.csv"
        if alt_csv.exists():
            csv_path = alt_csv
        else:
            print(f"❌ No CSV found for {bag_path.relative_to(ROOT_DIR)} – expected bag_information/object_descriptions.csv or visual_descriptions.csv. Skipping.")
            return

    print(f"🔍 {csv_path.relative_to(ROOT_DIR)}")
    df = pd.read_csv(csv_path)

    ensure_cols(df, [
        "object_analysis_output", "changes_analysis_output", "lidar_changes_output",
        "cause1_analysis_output", "cause2_analysis_output", "environment_aggregation",
        "zero_shot_environment_analysis", "self_refine_environment_output", "CoT_output",
        "aggregated_driving_environment", "vehicle_expert_output"  # may be empty if vehicle.py not run
    ])

    # PASS 1 – object detection (always needed)
    # Low-cost fallback: if textual descriptions exist, use them to populate object_analysis_output first
    text_cols_priority = [
        "description_drive_agent",
        "description_gpt4o",
        "description_gpt4omini",
        "description_llama",
        "description_mixtra",
        "description _llama3.2",
    ]
    available_text_cols = [c for c in text_cols_priority if c in df.columns]
    if available_text_cols:
        for idx, row in df.iterrows():
            if row["object_analysis_output"]:
                continue
            for c in available_text_cols:
                txt = str(row.get(c, "")).strip()
                if txt:
                    df.at[idx, "object_analysis_output"] = txt
                    break
        df.to_csv(csv_path, index=False)

    # Resolve image directory and filename column across layouts
    img_dir = bag_path / "img"
    if not img_dir.exists():
        alt_img = bag_path / "pic_sort"
        if alt_img.exists():
            img_dir = alt_img

    fname_col = None
    if "env_picture_title" in df.columns:
        fname_col = "env_picture_title"
    elif "Filename" in df.columns:
        fname_col = "Filename"

    for idx, row in df.iterrows():
        if row["object_analysis_output"]:
            continue
        if not fname_col or not img_dir.exists():
            continue
        img_name = str(row.get(fname_col, "")).strip()
        if not img_name:
            continue
        img_path = img_dir / img_name
        if not img_path.exists():
            continue
        df.at[idx, "object_analysis_output"] = object_analysis(encode_image(img_path))
        df.to_csv(csv_path, index=False)

    # PASS 2 – depending on framework ---------------------------------------
    last_idx = len(df) - 1
    if MAX_ROWS is not None:
        last_idx = min(last_idx, MAX_ROWS)
    for idx in range(1, last_idx + 1):
        prev_idx = idx - 1
        prev_vis, curr_vis = df.at[prev_idx, "object_analysis_output"], df.at[idx, "object_analysis_output"]
        prev_lidar, curr_lidar = df.at[prev_idx, "vehicle_expert_output"], df.at[idx, "vehicle_expert_output"]
        if not curr_vis:
            continue

        # ---------------- choose framework tasks ---------------------------
        if FRAMEWORK in ("cot", "full") and not df.at[idx, "CoT_output"]:
            df.at[idx, "CoT_output"] = cot(prev_vis, curr_vis, prev_lidar, curr_lidar)

        if FRAMEWORK in ("self-refine", "full") and not df.at[idx, "self_refine_environment_output"]:
            df.at[idx, "self_refine_environment_output"] = self_refine_objects(prev_vis, curr_vis, prev_lidar, curr_lidar)

        if FRAMEWORK in ("cot", "full"):
            # Visual + lidar change pipelines (needed by cot/full)
            if not df.at[idx, "changes_analysis_output"]:
                df.at[idx, "changes_analysis_output"] = visual_changes(prev_vis, curr_vis)
            if not df.at[idx, "lidar_changes_output"]:
                df.at[idx, "lidar_changes_output"] = lidar_changes_analysis(prev_lidar, curr_lidar)
            if not df.at[idx, "cause1_analysis_output"]:
                df.at[idx, "cause1_analysis_output"] = cause1_analysis(
                    df.at[idx, "changes_analysis_output"], df.at[idx, "lidar_changes_output"]
                )
            if not df.at[idx, "cause2_analysis_output"]:
                df.at[idx, "cause2_analysis_output"] = cause2_analysis(
                    df.at[idx, "changes_analysis_output"], df.at[idx, "lidar_changes_output"]
                )
            if not df.at[idx, "aggregated_driving_environment"]:
                df.at[idx, "aggregated_driving_environment"] = cause_aggregation(
                    df.at[idx, "cause1_analysis_output"], df.at[idx, "cause2_analysis_output"]
                )

        if FRAMEWORK in ("zero-shot", "full") and not df.at[idx, "zero_shot_environment_analysis"]:
            df.at[idx, "zero_shot_environment_analysis"] = zero_shot(prev_vis, curr_vis, prev_lidar, curr_lidar)

        # Minimal yes/no aggregation to show something for every framework
        if not df.at[idx, "environment_aggregation"]:
            df.at[idx, "environment_aggregation"] = aggregation(df.at[idx, "changes_analysis_output"] or curr_vis)

        df.to_csv(csv_path, index=False)
        print(f"DONE for Row {idx+1} done (framework: {FRAMEWORK})!")

if __name__ == "__main__":
    if ARGS.bags:
        bag_dirs = [Path(b) if os.path.isabs(b) else ROOT_DIR / b for b in ARGS.bags]
    else:
        bag_dirs = [d for d in ROOT_DIR.iterdir() if d.is_dir() and (d.name.startswith("hou-") or d.name in ("R1", "R2", "R3"))]
    for bag in bag_dirs:
        process_bag(bag if isinstance(bag, Path) else ROOT_DIR / bag)